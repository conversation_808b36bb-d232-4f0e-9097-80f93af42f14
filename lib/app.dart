import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import 'core/constants/app_constants.dart';
import 'core/providers/app_startup_provider.dart';
import 'core/router/app_router.dart';
import 'src/theme/theme_provider.dart';
import 'src/shared/widgets/loading_screen.dart';
import 'src/shared/widgets/error_screen.dart';

/// Main application widget
class FitGoApp extends ConsumerWidget {
  const FitGoApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final appStartupState = ref.watch(appStartupProvider);

    return appStartupState.when(
      loading: () => const MaterialApp(
        title: AppConstants.appName,
        home: LoadingScreen(),
        debugShowCheckedModeBanner: false,
      ),
      error: (error, stackTrace) => MaterialApp(
        title: AppConstants.appName,
        home: ErrorScreen(
          error: error.toString(),
          onRetry: () => ref.invalidate(appStartupProvider),
        ),
        debugShowCheckedModeBanner: false,
      ),
      data: (_) => _buildApp(ref),
    );
  }

  Widget _buildApp(WidgetRef ref) {
    final router = ref.watch(appRouterProvider);
    final themeMode = ref.watch(themeModeProvider);

    return MaterialApp.router(
      title: AppConstants.appName,
      debugShowCheckedModeBanner: false,
      theme: lightTheme,
      darkTheme: darkTheme,
      themeMode: themeMode,
      routerConfig: router,
    );
  }
}
