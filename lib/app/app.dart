import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../core/constants/app_constants.dart';
import '../shared/application/providers/app_startup_provider.dart';
import '../shared/presentation/widgets/loading_screen.dart';
import '../shared/presentation/widgets/error_screen.dart';
import 'router/app_router.dart';
import 'theme/app_theme.dart';

/// Main application widget
class FitGoApp extends ConsumerWidget {
  const FitGoApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final appStartupState = ref.watch(appStartupProvider);

    return appStartupState.when(
      loading:
          () => const MaterialApp(
            title: AppConstants.appName,
            home: LoadingScreen(),
            debugShowCheckedModeBanner: false,
          ),
      error:
          (error, stackTrace) => MaterialApp(
            title: AppConstants.appName,
            home: ErrorScreen(
              error: error.toString(),
              onRetry: () => ref.invalidate(appStartupProvider),
            ),
            debugShowCheckedModeBanner: false,
          ),
      data: (_) => _buildApp(ref),
    );
  }

  Widget _buildApp(WidgetRef ref) {
    final router = ref.watch(appRouterProvider);

    return MaterialApp.router(
      title: AppConstants.appName,
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      routerConfig: router,
    );
  }
}
