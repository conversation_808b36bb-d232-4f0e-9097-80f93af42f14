import 'package:get_it/get_it.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../../core/environment/environment_interface.dart';
import '../../core/local_storage/hive_helper.dart';
import '../../core/network/network_service.dart';
import '../../core/storage/storage_service.dart';
import '../../shared/infrastructure/supabase/supabase_service.dart';

final getIt = GetIt.instance;

/// Initialize all dependencies
Future<void> initializeDependencies() async {
  // Core services
  _registerCoreServices();

  // Infrastructure
  await _registerInfrastructure();
}

void _registerCoreServices() {
  // Environment - create a simple implementation
  getIt.registerLazySingleton<IEnvironment>(() => _SimpleEnvironment());

  // Storage service
  getIt.registerLazySingleton<StorageService>(() => StorageService());

  // Network service
  getIt.registerLazySingleton<NetworkService>(() => NetworkService());
}

/// Simple environment implementation for DI
class _SimpleEnvironment implements IEnvironment {
  @override
  String get bannerName => kDebugMode ? 'DEV' : 'PROD';

  @override
  Color get bannerColor => kDebugMode ? Colors.red : Colors.green;

  @override
  BoxName get boxName =>
      kDebugMode ? BoxName.developmentBox : BoxName.productionBox;

  @override
  String get apiBaseUrl => 'https://api.fitgo.app';

  @override
  String get unchangedVariable => '';

  @override
  String get supabaseUrl => 'https://wrevdlggsevlckprjrwm.supabase.co';

  @override
  String get supabaseAnonKey =>
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndyZXZkbGdnc2V2bGNrcHJqcndtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk3MTcsImV4cCI6MjA1NjYwNTcxN30.KRdBVWPAqDSCsyvYBE3ntrPqpH09KzUmnzbYONAFtzY';

  @override
  String get firebaseFunctionsUrl => '';

  @override
  String get googlePlacesApiKey => '';

  @override
  String get googleTextSearchPlaceUrl => '';

  @override
  String get googleAutoCompleteUrl => '';

  @override
  String get cloudFunctionsBaseUrl => '';

  @override
  bool get enableDioLogs => kDebugMode;
}

Future<void> _registerInfrastructure() async {
  // Supabase service
  getIt.registerLazySingleton<SupabaseService>(() => SupabaseService());
}

/// Clean up dependencies
Future<void> resetDependencies() async {
  await getIt.reset();
}
