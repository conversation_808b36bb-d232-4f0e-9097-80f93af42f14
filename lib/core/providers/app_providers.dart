import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../environment/environment_interface.dart';
import '../local_storage/hive_helper.dart';
import '../network/network_service.dart';
import '../storage/storage_service.dart';
import '../../shared/infrastructure/supabase/supabase_service.dart';

/// Environment provider
final environmentProvider = Provider<IEnvironment>((ref) {
  return _SimpleEnvironment();
});

/// Storage service provider
final storageServiceProvider = Provider<StorageService>((ref) {
  return StorageService();
});

/// Network service provider
final networkServiceProvider = Provider<NetworkService>((ref) {
  return NetworkService();
});

/// Supabase service provider
final supabaseServiceProvider = Provider<SupabaseService>((ref) {
  return SupabaseService();
});

/// Initialized storage service provider
final initializedStorageServiceProvider = FutureProvider<StorageService>((ref) async {
  final storageService = ref.read(storageServiceProvider);
  await storageService.initialize();
  return storageService;
});

/// Initialized Supabase service provider
final initializedSupabaseServiceProvider = FutureProvider<SupabaseService>((ref) async {
  final environment = ref.read(environmentProvider);
  final supabaseService = ref.read(supabaseServiceProvider);
  await supabaseService.initialize(environment);
  return supabaseService;
});

/// Simple environment implementation
class _SimpleEnvironment implements IEnvironment {
  @override
  String get bannerName => kDebugMode ? 'DEV' : 'PROD';

  @override
  Color get bannerColor => kDebugMode ? Colors.red : Colors.green;

  @override
  BoxName get boxName =>
      kDebugMode ? BoxName.developmentBox : BoxName.productionBox;

  @override
  String get apiBaseUrl => 'https://api.fitgo.app';

  @override
  String get unchangedVariable => '';

  @override
  String get supabaseUrl => 'https://wrevdlggsevlckprjrwm.supabase.co';

  @override
  String get supabaseAnonKey =>
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.KRdBVWPAqDSCsyvYBE3ntrPqpH09KzUmnzbYONAFtzY';

  @override
  String get firebaseFunctionsUrl => '';

  @override
  String get googlePlacesApiKey => '';

  @override
  String get googleTextSearchPlaceUrl => '';

  @override
  String get googleAutoCompleteUrl => '';

  @override
  String get cloudFunctionsBaseUrl => '';

  @override
  bool get enableDioLogs => kDebugMode;
}
