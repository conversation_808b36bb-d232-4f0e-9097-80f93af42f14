import 'package:hooks_riverpod/hooks_riverpod.dart';

import 'app_providers.dart';

/// App startup state
enum AppStartupState {
  loading,
  completed,
  error,
}

/// Provider that handles app initialization
final appStartupProvider = FutureProvider<AppStartupState>((ref) async {
  try {
    // Initialize storage
    await ref.read(initializedStorageServiceProvider.future);
    
    // Initialize Supabase
    await ref.read(initializedSupabaseServiceProvider.future);
    
    // Add other initialization logic here
    // - Analytics initialization
    // - Crash reporting setup
    // - etc.
    
    return AppStartupState.completed;
  } catch (error, stackTrace) {
    // Log error (you can replace with proper logging)
    print('App startup error: $error');
    print('Stack trace: $stackTrace');
    
    return AppStartupState.error;
  }
});
