import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../src/auth/presentation/auth_page_view.dart';
import '../../src/home/<USER>/home_page_view.dart';
import '../../start/splash/splash_view.dart';

/// App router provider
final appRouterProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: '/splash',
    routes: [
      GoRoute(
        path: '/splash',
        builder: (context, state) => const SplashView(),
      ),
      GoRoute(
        path: '/auth',
        builder: (context, state) => const AuthPageView(),
      ),
      GoRoute(
        path: '/home',
        builder: (context, state) => const HomePageView(),
      ),
    ],
  );
});
