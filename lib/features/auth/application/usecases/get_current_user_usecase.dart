import 'package:dartz/dartz.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../../domain/entities/auth_user_entity.dart';
import '../../domain/repositories/auth_repository.dart';

/// Use case for getting current user
class GetCurrentUserUseCase implements NoParamsUseCase<AuthUserEntity?> {
  final AuthRepository repository;

  const GetCurrentUserUseCase(this.repository);

  @override
  Future<Either<Failure, AuthUserEntity?>> call() async {
    return await repository.getCurrentUser();
  }
}
