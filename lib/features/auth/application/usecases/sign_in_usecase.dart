import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../../domain/entities/auth_user_entity.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../domain/value_objects/email.dart';
import '../../domain/value_objects/password.dart';

/// Use case for signing in a user
class SignInUseCase implements UseCase<AuthUserEntity, SignInParams> {
  final AuthRepository repository;

  const SignInUseCase(this.repository);

  @override
  Future<Either<Failure, AuthUserEntity>> call(SignInParams params) async {
    // Validate email
    final emailResult = Email.create(params.email);
    if (emailResult.isLeft()) {
      return Left(
        emailResult.fold((failure) => failure, (_) => throw Exception()),
      );
    }

    // Validate password
    final passwordResult = Password.create(params.password);
    if (passwordResult.isLeft()) {
      return Left(
        passwordResult.fold((failure) => failure, (_) => throw Exception()),
      );
    }

    // Perform sign in
    return await repository.signInWithEmailAndPassword(
      email: emailResult.getOrElse(() => throw Exception()),
      password: passwordResult.getOrElse(() => throw Exception()),
    );
  }
}

/// Parameters for sign in use case
class SignInParams extends Equatable {
  final String email;
  final String password;

  const SignInParams({required this.email, required this.password});

  @override
  List<Object?> get props => [email, password];
}
