import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../../domain/entities/auth_user_entity.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../domain/value_objects/email.dart';
import '../../domain/value_objects/password.dart';

/// Use case for signing up a user
class SignUpUseCase implements UseCase<AuthUserEntity, SignUpParams> {
  final AuthRepository repository;

  const SignUpUseCase(this.repository);

  @override
  Future<Either<Failure, AuthUserEntity>> call(SignUpParams params) async {
    // Validate email
    final emailResult = Email.create(params.email);
    if (emailResult.isLeft()) {
      return Left(emailResult.fold((failure) => failure, (_) => throw Exception()));
    }

    // Validate password
    final passwordResult = Password.create(params.password);
    if (passwordResult.isLeft()) {
      return Left(passwordResult.fold((failure) => failure, (_) => throw Exception()));
    }

    // Perform sign up
    return await repository.signUpWithEmailAndPassword(
      email: emailResult.getOrElse(() => throw Exception()),
      password: passwordResult.getOrElse(() => throw Exception()),
      name: params.name,
      surname: params.surname,
      phone: params.phone,
    );
  }
}

/// Parameters for sign up use case
class SignUpParams extends Equatable {
  final String email;
  final String password;
  final String? name;
  final String? surname;
  final String? phone;

  const SignUpParams({
    required this.email,
    required this.password,
    this.name,
    this.surname,
    this.phone,
  });

  @override
  List<Object?> get props => [email, password, name, surname, phone];
}
