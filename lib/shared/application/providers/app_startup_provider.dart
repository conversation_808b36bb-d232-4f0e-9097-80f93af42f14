import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../core/di/injection_container.dart';
import '../../../core/storage/storage_service.dart';
import '../../../shared/infrastructure/supabase/supabase_service.dart';

/// App startup state
enum AppStartupState {
  loading,
  completed,
  error,
}

/// Provider that handles app initialization
final appStartupProvider = FutureProvider<AppStartupState>((ref) async {
  try {
    // Initialize storage
    final storageService = getIt<StorageService>();
    await storageService.initialize();
    
    // Initialize Supabase
    final supabaseService = getIt<SupabaseService>();
    await supabaseService.initialize();
    
    // Add other initialization logic here
    // - Firebase initialization
    // - Analytics initialization
    // - Crash reporting setup
    // - etc.
    
    return AppStartupState.completed;
  } catch (error, stackTrace) {
    // Log error
    print('App startup error: $error');
    print('Stack trace: $stackTrace');
    
    return AppStartupState.error;
  }
});
