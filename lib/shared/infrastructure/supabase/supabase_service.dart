import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../core/environment/environment_interface.dart';

/// Supabase service for handling Supabase initialization and configuration
class SupabaseService {
  SupabaseClient? _client;

  /// Get Supabase client instance
  SupabaseClient get client {
    if (_client == null) {
      throw Exception('Supabase not initialized. Call initialize() first.');
    }
    return _client!;
  }

  /// Initialize Supabase
  Future<void> initialize(IEnvironment environment) async {
    await Supabase.initialize(
      url: environment.supabaseUrl,
      anonKey: environment.supabaseAnonKey,
      debug: environment.enableDioLogs,
    );

    _client = Supabase.instance.client;
  }

  /// Check if Supabase is initialized
  bool get isInitialized => _client != null;

  /// Get current user
  User? get currentUser => _client?.auth.currentUser;

  /// Check if user is authenticated
  bool get isAuthenticated => currentUser != null;

  /// Sign out current user
  Future<void> signOut() async {
    await _client?.auth.signOut();
  }
}
