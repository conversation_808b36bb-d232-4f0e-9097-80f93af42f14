import 'package:fitgo_app/src/auth/application/auth_provider.dart';
import 'package:fitgo_app/src/auth/domain/profile.dart';
import 'package:fitgo_app/src/app_provider.dart';
import 'package:fitgo_app/src/shared/constants/app_fonts.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/enums/regex_type.dart';
import 'package:fitgo_app/src/shared/enums/user_type.dart';
import 'package:fitgo_app/src/shared/extensions/build_context/navigator_ext.dart';
import 'package:fitgo_app/src/shared/extensions/build_context/screen_util_ext.dart';
import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';
import 'package:fitgo_app/src/shared/extensions/string_extensions.dart';
import 'package:fitgo_app/src/shared/widgets/buttons/app_bar_back_button.dart';
import 'package:fitgo_app/src/shared/widgets/buttons/raised_button_widget.dart';
import 'package:fitgo_app/src/shared/widgets/text_form_field/custom_text_form_field.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import 'package:fitgo_app/src/shared/constants/app_path.dart';
import 'package:fitgo_app/src/shared/widgets/buttons/language_button.dart';
import 'package:fitgo_app/src/shared/widgets/image/asset_image.dart';
import 'package:fitgo_app/src/shared/widgets/scaffold/onboarding_scaffold.dart';
import 'package:fitgo_app/src/theme/colors.dart';

part 'components/login_bottom_sheet.dart';
part 'components/register_bottom_sheet.dart';

class AuthPageView extends HookConsumerWidget {
  const AuthPageView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return OnboardingScaffold(
      topBar: Align(alignment: Alignment.topRight, child: LanguageButton()),
      sheet: const LoginBottomSheet(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const SizedBox(height: 100),
          AImage(imgPath: APath.appLogo, width: context.width / 2),
        ],
      ),
    );
  }
}
