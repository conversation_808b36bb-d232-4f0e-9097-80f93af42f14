part of '../auth_page_view.dart';

class LoginBottomSheet extends HookConsumerWidget {
  const LoginBottomSheet({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final emailCtrl = useTextEditingController();
    final passwordCtrl = useTextEditingController();
    final isLoading = useState(false);
    final formKey = useMemoized(() => GlobalKey<FormState>());

    void openRegister() {
      ref.read(isRegisterOpenProvider.notifier).state = true;
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: AColor.transparent,
        builder: (_) => const RegisterBottomSheet(),
      ).whenComplete(() {
        ref.read(isRegisterOpenProvider.notifier).state = false;
      });
    }

    Future<void> _submit() async {
      if (!(formKey.currentState?.validate() ?? false)) return;
      isLoading.value = true;
      try {
        final authRepo = ref.read(authRepositoryProvider);
        final profile = await authRepo.signInWithEmail(
          email: emailCtrl.text.trim(),
          password: passwordCtrl.text.trim(),
        );

        // Update the global user type provider based on profile role
        ref.read(currentUserTypeProvider.notifier).state = profile.role;

        if (context.mounted) {
          Navigator.of(context).pop();

          // Navigate based on user type
          if (profile.role == UserType.instructor) {
            context.go('/instructor-main');
          } else {
            // For students, check if they have any enrollments
            // For now, we'll assume they need to see course list
            // TODO: Check actual enrollment status from database
            context.go('/course-list');
          }
        }
      } catch (err) {
        if (context.mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(err.toString())));
        }
      } finally {
        isLoading.value = false;
      }
    }

    return Padding(
      padding: MediaQuery.of(context).viewInsets,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: const BoxDecoration(
          color: AColor.bottomSheetBackgroundColor,
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          spacing: 16,
          children: [
            Form(
              key: formKey,
              child: Column(
                spacing: 16,
                children: [
                  CustomTextFormField(
                    controller: emailCtrl,
                    headerText: 'E-mail'.hardcoded,
                    keyboardType: TextInputType.emailAddress,
                    textInputAction: TextInputAction.next,
                    validator: (value) => value.isValidMail(context),
                    regexType: RegexType.eMail,
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    spacing: 4,
                    children: [
                      CustomTextFormField(
                        controller: passwordCtrl,
                        headerText: 'Password'.hardcoded,
                        keyboardType: TextInputType.visiblePassword,
                        textInputAction: TextInputAction.done,
                        validator: (value) => value.isValidPassword(context),
                        regexType: RegexType.password,
                        obscureText: true,
                      ),
                      InkWell(
                        onTap: () {},
                        child: TextWidget(
                          'Forgot Password?'.hardcoded,
                          style: ATextStyle.medium,
                        ),
                      ),
                    ],
                  ),
                  RaisedButtonWidget(
                    width: double.infinity,
                    text: 'Sign In'.hardcoded,
                    borderRadius: 15,
                    fontColor: AColor.buttonTextColor,
                    bgColor: AColor.buttonColor,
                    fontStyle: ATextStyle.buttonText,
                    borderSide: BorderSide(
                      color: AColor.textSecondaryColor,
                      width: 2,
                    ),
                    onPressed: isLoading.value ? null : _submit,
                  ),
                ],
              ),
            ),

            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(height: 1, width: 10, color: AColor.white),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 8),
                  child: TextWidget('OR'.hardcoded),
                ),
                Container(height: 1, width: 10, color: AColor.white),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _SocialButton(icon: Icons.g_mobiledata, onTap: () {}),
                const SizedBox(width: 16),
                _SocialButton(icon: Icons.facebook, onTap: () {}),
                const SizedBox(width: 16),
                _SocialButton(icon: Icons.one_x_mobiledata, onTap: () {}),
              ],
            ),
            Row(children: [Expanded(child: Divider())]),
            InkWell(
              onTap: openRegister,
              child: TextWidget(
                'No Account? Register Now'.hardcoded,
                style: ATextStyle.medium.copyWith(
                  fontSize: 16,
                  fontFamily: AppFonts.poppins,
                ),
              ),
            ),
            SizedBox(height: 16),
          ],
        ),
      ),
    );
  }
}

class _SocialButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback onTap;
  const _SocialButton({required this.icon, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: CircleAvatar(
        radius: 24,
        backgroundColor: AColor.buttonColor,
        child: Icon(icon, color: AColor.white),
      ),
    );
  }
}
