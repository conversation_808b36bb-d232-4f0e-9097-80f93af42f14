import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';

class InstructorMainView extends HookConsumerWidget {
  const InstructorMainView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: AColor.black,
      body: DefaultTabController(
        length: 4,
        child: Scaffold(
          backgroundColor: AColor.black,
          appBar: AppBar(
            backgroundColor: AColor.black,
            elevation: 0,
            title: TextWidget(
              'FitGo Trainer'.hardcoded,
              style: ATextStyle.title.copyWith(color: AColor.white),
            ),
            centerTitle: true,
          ),
          body: const TabBarView(
            children: [
              InstructorHomeTab(),
              InstructorMembersTab(),
              InstructorProfileTab(),
              InstructorExploreTab(),
            ],
          ),
          bottomNavigationBar: Container(
            color: AColor.black,
            child: TabBar(
              labelColor: AColor.buttonColor,
              unselectedLabelColor: AColor.grey,
              indicatorColor: AColor.buttonColor,
              tabs: [
                Tab(
                  icon: const Icon(Icons.home),
                  text: 'Home'.hardcoded,
                ),
                Tab(
                  icon: const Icon(Icons.people),
                  text: 'Members'.hardcoded,
                ),
                Tab(
                  icon: const Icon(Icons.person),
                  text: 'Profile'.hardcoded,
                ),
                Tab(
                  icon: const Icon(Icons.explore),
                  text: 'Explore'.hardcoded,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// Home Tab for Instructors
class InstructorHomeTab extends StatelessWidget {
  const InstructorHomeTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextWidget(
            'Dashboard'.hardcoded,
            style: ATextStyle.title.copyWith(color: AColor.white),
          ),
          const SizedBox(height: 20),
          Expanded(
            child: GridView.count(
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              children: [
                _buildDashboardCard(
                  'My Courses'.hardcoded,
                  '12',
                  Icons.fitness_center,
                  AColor.buttonColor,
                ),
                _buildDashboardCard(
                  'Students'.hardcoded,
                  '45',
                  Icons.people,
                  Colors.green,
                ),
                _buildDashboardCard(
                  'Revenue'.hardcoded,
                  '\$2,450',
                  Icons.attach_money,
                  Colors.orange,
                ),
                _buildDashboardCard(
                  'Reviews'.hardcoded,
                  '4.8',
                  Icons.star,
                  Colors.yellow,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDashboardCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AColor.bottomSheetBackgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AColor.textSecondaryColor),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          TextWidget(
            value,
            style: ATextStyle.title.copyWith(color: AColor.white),
          ),
          const SizedBox(height: 4),
          TextWidget(
            title,
            style: ATextStyle.medium.copyWith(color: AColor.grey),
          ),
        ],
      ),
    );
  }
}

// Members Tab for Instructors
class InstructorMembersTab extends StatelessWidget {
  const InstructorMembersTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextWidget(
            'My Students'.hardcoded,
            style: ATextStyle.title.copyWith(color: AColor.white),
          ),
          const SizedBox(height: 20),
          Expanded(
            child: ListView.builder(
              itemCount: 10,
              itemBuilder: (context, index) {
                return Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AColor.bottomSheetBackgroundColor,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AColor.textSecondaryColor),
                  ),
                  child: Row(
                    children: [
                      CircleAvatar(
                        backgroundColor: AColor.buttonColor,
                        child: TextWidget(
                          'S${index + 1}',
                          style: ATextStyle.medium.copyWith(color: AColor.white),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            TextWidget(
                              'Student ${index + 1}',
                              style: ATextStyle.medium.copyWith(color: AColor.white),
                            ),
                            TextWidget(
                              'Active since 2 months',
                              style: ATextStyle.small.copyWith(color: AColor.grey),
                            ),
                          ],
                        ),
                      ),
                      Icon(Icons.arrow_forward_ios, color: AColor.grey, size: 16),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

// Profile Tab for Instructors
class InstructorProfileTab extends StatelessWidget {
  const InstructorProfileTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          CircleAvatar(
            radius: 50,
            backgroundColor: AColor.buttonColor,
            child: Icon(Icons.person, size: 50, color: AColor.white),
          ),
          const SizedBox(height: 16),
          TextWidget(
            'Instructor Name',
            style: ATextStyle.title.copyWith(color: AColor.white),
          ),
          const SizedBox(height: 8),
          TextWidget(
            'Fitness Trainer',
            style: ATextStyle.medium.copyWith(color: AColor.grey),
          ),
          const SizedBox(height: 32),
          _buildProfileOption('Edit Profile', Icons.edit),
          _buildProfileOption('My Courses', Icons.fitness_center),
          _buildProfileOption('Settings', Icons.settings),
          _buildProfileOption('Help & Support', Icons.help),
          _buildProfileOption('Logout', Icons.logout),
        ],
      ),
    );
  }

  Widget _buildProfileOption(String title, IconData icon) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AColor.bottomSheetBackgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AColor.textSecondaryColor),
      ),
      child: Row(
        children: [
          Icon(icon, color: AColor.buttonColor),
          const SizedBox(width: 16),
          Expanded(
            child: TextWidget(
              title,
              style: ATextStyle.medium.copyWith(color: AColor.white),
            ),
          ),
          Icon(Icons.arrow_forward_ios, color: AColor.grey, size: 16),
        ],
      ),
    );
  }
}

// Explore Tab for Instructors
class InstructorExploreTab extends StatelessWidget {
  const InstructorExploreTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextWidget(
            'Explore'.hardcoded,
            style: ATextStyle.title.copyWith(color: AColor.white),
          ),
          const SizedBox(height: 20),
          TextWidget(
            'Discover new training methods, connect with other trainers, and grow your business.',
            style: ATextStyle.medium.copyWith(color: AColor.grey),
          ),
          const SizedBox(height: 20),
          Expanded(
            child: Center(
              child: TextWidget(
                'Coming Soon!',
                style: ATextStyle.title.copyWith(color: AColor.buttonColor),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
