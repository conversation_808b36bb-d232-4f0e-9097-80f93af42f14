import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';
import 'package:fitgo_app/src/shared/widgets/buttons/raised_button_widget.dart';
import 'package:go_router/go_router.dart';

class CourseListView extends HookConsumerWidget {
  const CourseListView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: AColor.black,
      appBar: AppBar(
        backgroundColor: AColor.black,
        elevation: 0,
        title: TextWidget(
          'Available Courses'.hardcoded,
          style: ATextStyle.title.copyWith(color: AColor.white),
        ),
        centerTitle: true,
      ),
      body: Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextWidget(
              'Choose a course to start your fitness journey!'.hardcoded,
              style: ATextStyle.medium.copyWith(color: AColor.grey),
            ),
            const SizedBox(height: 20),
            Expanded(
              child: ListView.builder(
                itemCount: 8,
                itemBuilder: (context, index) {
                  return _buildCourseCard(context, index);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCourseCard(BuildContext context, int index) {
    final courses = [
      {
        'title': 'Beginner Fitness',
        'instructor': 'John Doe',
        'duration': '4 weeks',
        'price': '\$29.99',
        'rating': '4.8',
        'students': '120',
        'description': 'Perfect for beginners starting their fitness journey',
      },
      {
        'title': 'Weight Loss Program',
        'instructor': 'Sarah Smith',
        'duration': '8 weeks',
        'price': '\$49.99',
        'rating': '4.9',
        'students': '85',
        'description': 'Comprehensive weight loss program with nutrition',
      },
      {
        'title': 'Muscle Building',
        'instructor': 'Mike Johnson',
        'duration': '12 weeks',
        'price': '\$79.99',
        'rating': '4.7',
        'students': '95',
        'description': 'Advanced muscle building and strength training',
      },
      {
        'title': 'Yoga & Flexibility',
        'instructor': 'Emma Wilson',
        'duration': '6 weeks',
        'price': '\$39.99',
        'rating': '4.9',
        'students': '150',
        'description': 'Improve flexibility and mental wellness',
      },
      {
        'title': 'HIIT Training',
        'instructor': 'David Brown',
        'duration': '4 weeks',
        'price': '\$34.99',
        'rating': '4.6',
        'students': '75',
        'description': 'High-intensity interval training for quick results',
      },
      {
        'title': 'Cardio Blast',
        'instructor': 'Lisa Garcia',
        'duration': '6 weeks',
        'price': '\$44.99',
        'rating': '4.8',
        'students': '110',
        'description': 'Cardiovascular endurance and fat burning',
      },
      {
        'title': 'Strength Training',
        'instructor': 'Tom Anderson',
        'duration': '10 weeks',
        'price': '\$69.99',
        'rating': '4.7',
        'students': '88',
        'description': 'Build strength and muscle definition',
      },
      {
        'title': 'Pilates Core',
        'instructor': 'Anna Martinez',
        'duration': '8 weeks',
        'price': '\$54.99',
        'rating': '4.9',
        'students': '92',
        'description': 'Core strengthening and body alignment',
      },
    ];

    final course = courses[index % courses.length];

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AColor.bottomSheetBackgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AColor.textSecondaryColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextWidget(
                      course['title']!,
                      style: ATextStyle.large.copyWith(
                        color: AColor.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    TextWidget(
                      'by ${course['instructor']}',
                      style: ATextStyle.medium.copyWith(
                        color: AColor.buttonColor,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: AColor.buttonColor,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: TextWidget(
                  course['price']!,
                  style: ATextStyle.medium.copyWith(
                    color: AColor.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          TextWidget(
            course['description']!,
            style: ATextStyle.medium.copyWith(color: AColor.grey),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Icon(Icons.access_time, color: AColor.grey, size: 16),
              const SizedBox(width: 4),
              TextWidget(
                course['duration']!,
                style: ATextStyle.small.copyWith(color: AColor.grey),
              ),
              const SizedBox(width: 16),
              Icon(Icons.star, color: Colors.yellow, size: 16),
              const SizedBox(width: 4),
              TextWidget(
                course['rating']!,
                style: ATextStyle.small.copyWith(color: AColor.grey),
              ),
              const SizedBox(width: 16),
              Icon(Icons.people, color: AColor.grey, size: 16),
              const SizedBox(width: 4),
              TextWidget(
                '${course['students']} students',
                style: ATextStyle.small.copyWith(color: AColor.grey),
              ),
            ],
          ),
          const SizedBox(height: 16),
          RaisedButtonWidget(
            width: double.infinity,
            text: 'Enroll Now'.hardcoded,
            borderRadius: 8,
            fontColor: AColor.buttonTextColor,
            bgColor: AColor.buttonColor,
            fontStyle: ATextStyle.buttonText,
            borderSide: BorderSide(color: AColor.textSecondaryColor, width: 1),
            onPressed: () {
              _showEnrollDialog(context, course['title']!);
            },
          ),
        ],
      ),
    );
  }

  void _showEnrollDialog(BuildContext context, String courseTitle) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: AColor.bottomSheetBackgroundColor,
            title: TextWidget(
              'Enroll in Course'.hardcoded,
              style: ATextStyle.title.copyWith(color: AColor.white),
            ),
            content: TextWidget(
              'Do you want to enroll in "$courseTitle"?',
              style: ATextStyle.medium.copyWith(color: AColor.grey),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: TextWidget(
                  'Cancel'.hardcoded,
                  style: ATextStyle.medium.copyWith(color: AColor.grey),
                ),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();

                  // Show success message
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Successfully enrolled in $courseTitle!'),
                      backgroundColor: AColor.buttonColor,
                    ),
                  );

                  // Navigate to student main view after enrollment
                  Future.delayed(const Duration(seconds: 1), () {
                    if (context.mounted) {
                      context.go('/student-main');
                    }
                  });
                },
                child: TextWidget(
                  'Enroll'.hardcoded,
                  style: ATextStyle.medium.copyWith(color: AColor.buttonColor),
                ),
              ),
            ],
          ),
    );
  }
}
