import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/src/shared/widgets/text_widget/text_widget.dart';
import 'package:fitgo_app/src/shared/constants/app_text_style.dart';
import 'package:fitgo_app/src/shared/extensions/string_hardcoded.dart';

class StudentMainView extends HookConsumerWidget {
  const StudentMainView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: AColor.black,
      body: DefaultTabController(
        length: 4,
        child: Scaffold(
          backgroundColor: AColor.black,
          appBar: AppBar(
            backgroundColor: AColor.black,
            elevation: 0,
            title: TextWidget(
              'FitGo Student'.hardcoded,
              style: ATextStyle.title.copyWith(color: AColor.white),
            ),
            centerTitle: true,
          ),
          body: const TabBarView(
            children: [
              StudentExerciseTab(),
              StudentNutritionTab(),
              StudentProgressTab(),
              StudentExploreTab(),
            ],
          ),
          bottomNavigationBar: Container(
            color: AColor.black,
            child: TabBar(
              labelColor: AColor.buttonColor,
              unselectedLabelColor: AColor.grey,
              indicatorColor: AColor.buttonColor,
              tabs: [
                Tab(
                  icon: const Icon(Icons.fitness_center),
                  text: 'Exercise'.hardcoded,
                ),
                Tab(
                  icon: const Icon(Icons.restaurant),
                  text: 'Nutrition'.hardcoded,
                ),
                Tab(
                  icon: const Icon(Icons.trending_up),
                  text: 'Progress'.hardcoded,
                ),
                Tab(
                  icon: const Icon(Icons.explore),
                  text: 'Explore'.hardcoded,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// Exercise Tab for Students
class StudentExerciseTab extends StatelessWidget {
  const StudentExerciseTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextWidget(
            'Today\'s Workout'.hardcoded,
            style: ATextStyle.title.copyWith(color: AColor.white),
          ),
          const SizedBox(height: 20),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AColor.bottomSheetBackgroundColor,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AColor.textSecondaryColor),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextWidget(
                  'Upper Body Strength',
                  style: ATextStyle.large.copyWith(color: AColor.white),
                ),
                const SizedBox(height: 8),
                TextWidget(
                  '45 minutes • 8 exercises',
                  style: ATextStyle.medium.copyWith(color: AColor.grey),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {},
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AColor.buttonColor,
                          foregroundColor: AColor.white,
                        ),
                        child: Text('Start Workout'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          TextWidget(
            'Exercise List'.hardcoded,
            style: ATextStyle.large.copyWith(color: AColor.white),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: ListView.builder(
              itemCount: 8,
              itemBuilder: (context, index) {
                final exercises = [
                  'Push-ups',
                  'Pull-ups',
                  'Bench Press',
                  'Shoulder Press',
                  'Bicep Curls',
                  'Tricep Dips',
                  'Plank',
                  'Mountain Climbers',
                ];
                return Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AColor.bottomSheetBackgroundColor,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: AColor.textSecondaryColor),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.fitness_center, color: AColor.buttonColor),
                      const SizedBox(width: 12),
                      Expanded(
                        child: TextWidget(
                          exercises[index],
                          style: ATextStyle.medium.copyWith(color: AColor.white),
                        ),
                      ),
                      TextWidget(
                        '3x12',
                        style: ATextStyle.small.copyWith(color: AColor.grey),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

// Nutrition Tab for Students
class StudentNutritionTab extends StatelessWidget {
  const StudentNutritionTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextWidget(
            'Today\'s Nutrition'.hardcoded,
            style: ATextStyle.title.copyWith(color: AColor.white),
          ),
          const SizedBox(height: 20),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AColor.bottomSheetBackgroundColor,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AColor.textSecondaryColor),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildMacroCard('Calories', '1,850', '2,200', Colors.orange),
                    _buildMacroCard('Protein', '120g', '150g', Colors.red),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildMacroCard('Carbs', '180g', '220g', Colors.blue),
                    _buildMacroCard('Fat', '65g', '80g', Colors.green),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          TextWidget(
            'Meal Plan'.hardcoded,
            style: ATextStyle.large.copyWith(color: AColor.white),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: ListView(
              children: [
                _buildMealCard('Breakfast', 'Oatmeal with berries', '350 cal'),
                _buildMealCard('Lunch', 'Grilled chicken salad', '450 cal'),
                _buildMealCard('Snack', 'Greek yogurt', '150 cal'),
                _buildMealCard('Dinner', 'Salmon with vegetables', '500 cal'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMacroCard(String title, String current, String target, Color color) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(12),
        margin: const EdgeInsets.symmetric(horizontal: 4),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            TextWidget(
              title,
              style: ATextStyle.small.copyWith(color: AColor.grey),
            ),
            const SizedBox(height: 4),
            TextWidget(
              current,
              style: ATextStyle.medium.copyWith(color: color),
            ),
            TextWidget(
              '/ $target',
              style: ATextStyle.small.copyWith(color: AColor.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMealCard(String mealType, String description, String calories) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AColor.bottomSheetBackgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AColor.textSecondaryColor),
      ),
      child: Row(
        children: [
          Icon(Icons.restaurant, color: AColor.buttonColor),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextWidget(
                  mealType,
                  style: ATextStyle.medium.copyWith(color: AColor.white),
                ),
                TextWidget(
                  description,
                  style: ATextStyle.small.copyWith(color: AColor.grey),
                ),
              ],
            ),
          ),
          TextWidget(
            calories,
            style: ATextStyle.small.copyWith(color: AColor.buttonColor),
          ),
        ],
      ),
    );
  }
}

// Progress Tab for Students
class StudentProgressTab extends StatelessWidget {
  const StudentProgressTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextWidget(
            'Your Progress'.hardcoded,
            style: ATextStyle.title.copyWith(color: AColor.white),
          ),
          const SizedBox(height: 20),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AColor.bottomSheetBackgroundColor,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AColor.textSecondaryColor),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildProgressCard('Weight', '75.2 kg', '-2.3 kg'),
                    _buildProgressCard('BMI', '22.1', '-0.8'),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildProgressCard('Workouts', '24', '+6'),
                    _buildProgressCard('Streak', '12 days', '+5'),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          TextWidget(
            'Recent Activities'.hardcoded,
            style: ATextStyle.large.copyWith(color: AColor.white),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: ListView.builder(
              itemCount: 5,
              itemBuilder: (context, index) {
                return Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AColor.bottomSheetBackgroundColor,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: AColor.textSecondaryColor),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.check_circle, color: AColor.buttonColor),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            TextWidget(
                              'Upper Body Workout',
                              style: ATextStyle.medium.copyWith(color: AColor.white),
                            ),
                            TextWidget(
                              '${index + 1} days ago • 45 min',
                              style: ATextStyle.small.copyWith(color: AColor.grey),
                            ),
                          ],
                        ),
                      ),
                      TextWidget(
                        '320 cal',
                        style: ATextStyle.small.copyWith(color: AColor.buttonColor),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressCard(String title, String value, String change) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(12),
        margin: const EdgeInsets.symmetric(horizontal: 4),
        decoration: BoxDecoration(
          color: AColor.black,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AColor.textSecondaryColor),
        ),
        child: Column(
          children: [
            TextWidget(
              title,
              style: ATextStyle.small.copyWith(color: AColor.grey),
            ),
            const SizedBox(height: 4),
            TextWidget(
              value,
              style: ATextStyle.medium.copyWith(color: AColor.white),
            ),
            TextWidget(
              change,
              style: ATextStyle.small.copyWith(color: AColor.buttonColor),
            ),
          ],
        ),
      ),
    );
  }
}

// Explore Tab for Students
class StudentExploreTab extends StatelessWidget {
  const StudentExploreTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextWidget(
            'Explore'.hardcoded,
            style: ATextStyle.title.copyWith(color: AColor.white),
          ),
          const SizedBox(height: 20),
          TextWidget(
            'Discover new workouts, nutrition tips, and connect with the fitness community.',
            style: ATextStyle.medium.copyWith(color: AColor.grey),
          ),
          const SizedBox(height: 20),
          Expanded(
            child: Center(
              child: TextWidget(
                'Coming Soon!',
                style: ATextStyle.title.copyWith(color: AColor.buttonColor),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
