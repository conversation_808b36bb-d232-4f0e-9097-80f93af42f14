import 'package:fitgo_app/src/shared/constants/app_path.dart';
import 'package:fitgo_app/src/shared/extensions/build_context/screen_util_ext.dart';
import 'package:fitgo_app/src/shared/widgets/image/asset_image.dart';
import 'package:fitgo_app/src/theme/colors.dart';
import 'package:fitgo_app/src/app_provider.dart';
import 'package:fitgo_app/src/shared/enums/user_type.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';

class SplashView extends HookConsumerWidget {
  const SplashView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Listen to auth state and onboarding completion
    ref.listen<bool>(isAuthenticatedProvider, (previous, next) {
      if (next) {
        // User is authenticated, go to home
        context.go('/home');
      }
    });

    ref.listen<bool>(onboardingCompletedProvider, (previous, next) {
      if (next && !ref.read(isAuthenticatedProvider)) {
        // Onboarding completed but not authenticated, go to auth
        context.go('/auth');
      }
    });

    // Auto-navigate after splash delay
    Future.delayed(const Duration(seconds: 2), () {
      if (context.mounted) {
        final isAuthenticated = ref.read(isAuthenticatedProvider);
        final onboardingCompleted = ref.read(onboardingCompletedProvider);
        final userType = ref.read(currentUserTypeProvider);

        if (isAuthenticated) {
          // Navigate based on user role
          if (userType == UserType.instructor) {
            context.go('/instructor-main');
          } else {
            // Student - check enrollment status
            final isEnrolled = ref.read(studentEnrollmentStatusProvider);
            if (isEnrolled) {
              context.go('/student-main');
            } else {
              context.go('/course-list');
            }
          }
        } else if (onboardingCompleted) {
          context.go('/auth');
        } else if (userType != null) {
          // User has selected type but hasn't completed onboarding
          context.go('/onboarding');
        } else {
          // First time user, show landing page
          context.go('/landing');
        }
      }
    });

    return Container(
      decoration: const BoxDecoration(color: AColor.black),
      child: Center(
        child: AImage(imgPath: APath.appLogo, width: context.width / 2),
      ),
    );
  }
}
