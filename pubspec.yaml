name: fitgo_app
description: "A new Flutter project."

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.7.0-323.0.dev


dependencies:
  flutter:
    sdk: flutter

  ## Localization management
  flutter_localizations:
    sdk: flutter
  intl: any

  ## Navigation
  go_router: ^14.6.2
  auto_route: ^10.0.1
  url_launcher: ^6.3.1

  ## State Management
  hooks_riverpod: ^2.6.1
  flutter_hooks: ^0.21.2

  ## Functional Programming & DDD
  dartz: ^0.10.1
  equatable: ^2.0.7

  ## Network & Connectivity
  connectivity_plus: ^6.1.0
  shared_preferences: ^2.4.1

  ## Animations
  flutter_animate: ^4.5.2

  ## Local storage & file tools
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  ## Code design & performans tools
  freezed_annotation: ^3.0.0
  json_annotation: ^4.9.0
  data_channel: ^2.0.0+1

  ## Authentication & Social
  supabase_flutter: ^2.9.0
  google_sign_in: ^6.2.1
  flutter_facebook_auth: ^7.1.1
  sign_in_with_apple: ^6.1.3

  ## Media & Files
  cached_network_image: ^3.4.1
  flutter_cache_manager: ^3.4.1
  image_picker: ^1.1.2
  video_player: ^2.9.2
  chewie: ^1.8.5
  file_picker: ^8.1.4
  path_provider: ^2.1.5

  ## Charts & Analytics
  fl_chart: ^1.0.0
  syncfusion_flutter_charts: ^28.1.35

  ## Payments
  in_app_purchase: ^3.2.1

  ## Push Notifications (Firebase removed for now)
  # firebase_core: ^3.8.1
  # firebase_messaging: ^15.1.5
  # firebase_analytics: ^11.3.5

  ## UI Components
  cupertino_icons: ^1.0.8
  flutter_svg: ^2.1.0
  styled_text: ^8.1.0
  animations: ^2.0.11
  shimmer: ^3.0.0
  pull_to_refresh: ^2.0.0
  infinite_scroll_pagination: ^4.1.0
  lottie: ^3.1.3

  ## Utilities
  logger_plus: ^0.0.6+4
  uuid: ^4.5.1
  permission_handler: ^11.3.1
  share_plus: ^10.1.2
  device_info_plus: ^11.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter


  flutter_lints: ^5.0.0
  riverpod_lint: ^2.6.5
  build_runner: ^2.4.15
  freezed: ^3.0.6
  json_serializable: ^6.9.5
  auto_route_generator: ^10.0.1
  flutter_gen_runner: ^5.10.0
  ## Linter
  very_good_analysis: ^7.0.0

flutter_gen:
  output: lib/src/shared/constants/
  line_length: 120
  integrations:
    flutter_svg: true

flutter:
  uses-material-design: true

  # Enable generation of localized Strings from arb files.
  generate: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/fonts/

  fonts:
    - family: Poppins
      fonts:
        - asset: assets/fonts/Poppins-Regular.ttf
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-VariableFont_wdth,wght.ttf
    - family: Lato
      fonts:
        - asset: assets/fonts/Lato-Regular.ttf
    - family: Oswald
      fonts:
        - asset: assets/fonts/Oswald-VariableFont_wght.ttf